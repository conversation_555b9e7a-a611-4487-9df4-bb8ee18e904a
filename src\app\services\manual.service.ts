import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment.dev';
import { AngularFireStorage } from '@angular/fire/compat/storage';
import { GenericResponse } from '@app/models/backend/generic-response';

// Modelos
export class Manual {
  id: number = 0;
  nombre: string = '';
  tipo: string = '';
  archivo: string = '';
  isActive: boolean = true;
  userCreateId: number = 0;
  createdAt: string = '';
  updatedAt: string = '';
  tipoText: string = '';

  /**
   * Función auxiliar para formatear fechas de manera segura
   * @param dateValue Valor de fecha a formatear
   * @returns Fecha formateada como string ISO o string vacío si es inválida
   */
  private static formatDateSafely(dateValue: any): string {
    if (!dateValue) return '';

    try {
      // Si es un array (formato del backend: [año, mes, día, hora, minuto, segundo, nanosegundos])
      if (Array.isArray(dateValue)) {
        // Verificar que tenga al menos año, mes y día
        if (dateValue.length >= 3) {
          // Ajustar el mes (en JavaScript los meses van de 0 a 11)
          const year = dateValue[0];
          const month = dateValue[1] - 1; // Restar 1 al mes
          const day = dateValue[2];

          // Si tiene hora, minuto, segundo
          let hour = 0,
            minute = 0,
            second = 0,
            millisecond = 0;
          if (dateValue.length >= 6) {
            hour = dateValue[3];
            minute = dateValue[4];
            second = dateValue[5];

            // Si tiene nanosegundos, convertir a milisegundos
            if (dateValue.length >= 7 && dateValue[6]) {
              // Convertir nanosegundos a milisegundos (dividir por 1,000,000)
              millisecond = Math.floor(dateValue[6] / 1000000);
            }
          }

          const date = new Date(
            year,
            month,
            day,
            hour,
            minute,
            second,
            millisecond
          );

          // Verificar si la fecha es válida
          if (isNaN(date.getTime())) {
            return '';
          }
          return date.toISOString();
        }
        return ''; // Array incompleto
      }

      // Si ya es un string, verificar si es una fecha válida
      if (typeof dateValue === 'string') {
        // Si ya es un string ISO válido, devolverlo tal cual
        if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(dateValue)) {
          return dateValue;
        }

        // Intentar convertir el string a fecha
        const date = new Date(dateValue);
        // Verificar si la fecha es válida
        if (isNaN(date.getTime())) {
          return '';
        }
        return date.toISOString();
      }

      // Si es un objeto Date, convertirlo a string ISO
      if (dateValue instanceof Date) {
        // Verificar si la fecha es válida
        if (isNaN(dateValue.getTime())) {
          return '';
        }
        return dateValue.toISOString();
      }

      // Si es otro tipo (número, etc.), intentar convertirlo a fecha
      const date = new Date(dateValue);
      // Verificar si la fecha es válida
      if (isNaN(date.getTime())) {
        return '';
      }
      return date.toISOString();
    } catch (error) {
      // Si hay cualquier error, devolver string vacío
      console.warn('Error al formatear fecha:', error);
      return '';
    }
  }

  static cast(data: any): Manual {
    const manual = new Manual();

    // Mapear propiedades del formato antiguo al nuevo si es necesario
    if (data) {
      if (data.is_active !== undefined && data.isActive === undefined) {
        data.isActive = data.is_active;
      }
      if (
        data.user_create_id !== undefined &&
        data.userCreateId === undefined
      ) {
        data.userCreateId = data.user_create_id;
      }
      if (data.created_at !== undefined && data.createdAt === undefined) {
        data.createdAt = data.created_at;
      }
      if (data.updated_at !== undefined && data.updatedAt === undefined) {
        data.updatedAt = data.updated_at;
      }
      if (data.tipo_text !== undefined && data.tipoText === undefined) {
        data.tipoText = data.tipo_text;
      }
    }

    Object.assign(manual, data);

    // Formatear fechas de manera segura
    if (manual.createdAt && typeof manual.createdAt !== 'string') {
      manual.createdAt = Manual.formatDateSafely(manual.createdAt);
    }

    if (manual.updatedAt && typeof manual.updatedAt !== 'string') {
      manual.updatedAt = Manual.formatDateSafely(manual.updatedAt);
    }

    return manual;
  }
}

export class ManualList {
  id: number = 0;
  index: number = 0; // Para mostrar el número de fila en la tabla
  nombre: string = '';
  tipo: string = '';
  tipoText: string = '';
  archivo: string = '';
  isActive: boolean = true;
  userCreateId: number = 0;
  userUpdateId: number = 0;
  userDeleteId: number = 0;
  createdAt: string = '';
  updatedAt: string = '';
  deletedAt: string = '';

  // Propiedades para compatibilidad con el formato antiguo
  tipo_text: string = '';
  is_active: boolean = true;
  user_create_id: number = 0;
  user_update_id: number = 0;
  user_delete_id: number = 0;
  created_at: string = '';
  updated_at: string = '';
  deleted_at: string = '';

  /**
   * Función auxiliar para formatear fechas de manera segura
   * @param dateValue Valor de fecha a formatear
   * @returns Fecha formateada como string ISO o string vacío si es inválida
   */
  private static formatDateSafely(dateValue: any): string {
    if (!dateValue) return '';

    try {
      // Si es un array (formato del backend: [año, mes, día, hora, minuto, segundo, nanosegundos])
      if (Array.isArray(dateValue)) {
        // Verificar que tenga al menos año, mes y día
        if (dateValue.length >= 3) {
          // Ajustar el mes (en JavaScript los meses van de 0 a 11)
          const year = dateValue[0];
          const month = dateValue[1] - 1; // Restar 1 al mes
          const day = dateValue[2];

          // Si tiene hora, minuto, segundo
          let hour = 0,
            minute = 0,
            second = 0,
            millisecond = 0;
          if (dateValue.length >= 6) {
            hour = dateValue[3];
            minute = dateValue[4];
            second = dateValue[5];

            // Si tiene nanosegundos, convertir a milisegundos
            if (dateValue.length >= 7 && dateValue[6]) {
              // Convertir nanosegundos a milisegundos (dividir por 1,000,000)
              millisecond = Math.floor(dateValue[6] / 1000000);
            }
          }

          const date = new Date(
            year,
            month,
            day,
            hour,
            minute,
            second,
            millisecond
          );

          // Verificar si la fecha es válida
          if (isNaN(date.getTime())) {
            return '';
          }
          return date.toISOString();
        }
        return ''; // Array incompleto
      }

      // Si ya es un string, verificar si es una fecha válida
      if (typeof dateValue === 'string') {
        // Si ya es un string ISO válido, devolverlo tal cual
        if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(dateValue)) {
          return dateValue;
        }

        // Intentar convertir el string a fecha
        const date = new Date(dateValue);
        // Verificar si la fecha es válida
        if (isNaN(date.getTime())) {
          return '';
        }
        return date.toISOString();
      }

      // Si es un objeto Date, convertirlo a string ISO
      if (dateValue instanceof Date) {
        // Verificar si la fecha es válida
        if (isNaN(dateValue.getTime())) {
          return '';
        }
        return dateValue.toISOString();
      }

      // Si es otro tipo (número, etc.), intentar convertirlo a fecha
      const date = new Date(dateValue);
      // Verificar si la fecha es válida
      if (isNaN(date.getTime())) {
        return '';
      }
      return date.toISOString();
    } catch (error) {
      // Si hay cualquier error, devolver string vacío
      console.warn('Error al formatear fecha:', error);
      return '';
    }
  }

  static cast(data: any): ManualList {
    const manualList = new ManualList();

    // Mapear propiedades del formato antiguo al nuevo si es necesario
    if (data) {
      if (data.is_active !== undefined && data.isActive === undefined) {
        data.isActive = data.is_active;
      }
      if (
        data.user_create_id !== undefined &&
        data.userCreateId === undefined
      ) {
        data.userCreateId = data.user_create_id;
      }
      if (
        data.user_update_id !== undefined &&
        data.userUpdateId === undefined
      ) {
        data.userUpdateId = data.user_update_id;
      }
      if (
        data.user_delete_id !== undefined &&
        data.userDeleteId === undefined
      ) {
        data.userDeleteId = data.user_delete_id;
      }
      if (data.created_at !== undefined && data.createdAt === undefined) {
        data.createdAt = data.created_at;
      }
      if (data.updated_at !== undefined && data.updatedAt === undefined) {
        data.updatedAt = data.updated_at;
      }
      if (data.deleted_at !== undefined && data.deletedAt === undefined) {
        data.deletedAt = data.deleted_at;
      }
      if (data.tipo_text !== undefined && data.tipoText === undefined) {
        data.tipoText = data.tipo_text;
      }
    }

    Object.assign(manualList, data);

    // Asegurarse de que tipoText se genere correctamente
    if (manualList.tipo && !manualList.tipoText) {
      manualList.tipoText = ManualList.getTipoText(manualList.tipo);
      manualList.tipo_text = manualList.tipoText; // Para compatibilidad
    }

    // Formatear fechas de manera segura
    if (manualList.createdAt && typeof manualList.createdAt !== 'string') {
      manualList.createdAt = ManualList.formatDateSafely(manualList.createdAt);
      manualList.created_at = manualList.createdAt; // Para compatibilidad
    }

    if (manualList.updatedAt && typeof manualList.updatedAt !== 'string') {
      manualList.updatedAt = ManualList.formatDateSafely(manualList.updatedAt);
      manualList.updated_at = manualList.updatedAt; // Para compatibilidad
    }

    if (manualList.deletedAt && typeof manualList.deletedAt !== 'string') {
      manualList.deletedAt = ManualList.formatDateSafely(manualList.deletedAt);
      manualList.deleted_at = manualList.deletedAt; // Para compatibilidad
    }

    // Mantener sincronizados los campos antiguos y nuevos
    manualList.is_active = manualList.isActive;
    manualList.user_create_id = manualList.userCreateId;
    manualList.user_update_id = manualList.userUpdateId;
    manualList.user_delete_id = manualList.userDeleteId;
    manualList.created_at = manualList.createdAt;
    manualList.updated_at = manualList.updatedAt;
    manualList.deleted_at = manualList.deletedAt;

    return manualList;
  }

  static getTipoText(tipo: string): string {
    switch (tipo ? tipo.toUpperCase() : '') {
      case 'S':
        return 'Manual de Software';
      case 'B':
        return 'Gestión de Backlog';
      case 'M':
        return 'Vodafone Micropyme';
      case 'R':
        return 'Vodafone Residencial';
      case 'T':
        return 'Tarifario';
      case 'O':
        return 'Otro';
      default:
        return '';
    }
  }

  static casts(dataArray: any[]): ManualList[] {
    return dataArray.map((data) => ManualList.cast(data));
  }
}

export class Pagination {
  page: number = 1;
  perPage: number = 10;
  search: string = '';
  column: string = '';
  order: string = 'desc';
}

export class PaginationResult {
  currentPage: number = 0;
  data: any[] = [];
  from: number = 0;
  lastPage: number = 0;
  perPage: number = 0;
  to: number = 0;
  total: number = 0;

  static cast(data: any): PaginationResult {
    const result = new PaginationResult();
    Object.assign(result, data);
    return result;
  }
}

@Injectable({
  providedIn: 'root',
})
export class ManualService {
  private baseUrl = environment.url + 'api/manuales';
  private listSubject = new BehaviorSubject<ManualList[]>([]);
  public listObserver$ = this.listSubject.asObservable();

  constructor(private http: HttpClient, private storage: AngularFireStorage) {}

  /**
   * Obtiene todos los manuales
   * @returns Observable con la respuesta
   */
  getAll(): Observable<GenericResponse<ManualList[]>> {
    return this.http
      .get<GenericResponse<ManualList[]>>(`${this.baseUrl}/all`)
      .pipe(
        catchError((error) => {
          console.error('Error al obtener todos los manuales:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Obtiene un manual por su ID
   * @param id ID del manual
   * @returns Observable con la respuesta
   */
  getById(id: number): Observable<GenericResponse<Manual>> {
    return this.http.get<GenericResponse<Manual>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Registra un nuevo manual
   * @param data Datos del manual o FormData
   * @returns Observable con la respuesta
   */
  register(data: Manual | FormData): Observable<GenericResponse<ManualList>> {
    // Verificar si es FormData y tiene los datos necesarios
    if (data instanceof FormData) {
      // Verificar si tiene userAuthId (campo esperado por el backend)
      if (!data.has('userAuthId')) {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          try {
            const user = JSON.parse(userStr);
            if (user && user.id) {
              data.append('userAuthId', user.id.toString());

              // Mantener los campos anteriores por compatibilidad
              if (!data.has('userCreateId')) {
                data.append('userCreateId', user.id.toString());
              }
              if (!data.has('user_create_id')) {
                data.append('user_create_id', user.id.toString());
              }
            }
          } catch (error) {
            // Error al parsear el usuario del localStorage
          }
        }
      }

      // Si es FormData y contiene un archivo, primero subimos a Firebase
      if (data.has('file')) {
        const file = data.get('file') as File;
        if (file) {
          // Crear un nombre único para el archivo
          const filePath = `manuales/${new Date().getTime()}_${file.name}`;
          const fileRef = this.storage.ref(filePath);
          const task = this.storage.upload(filePath, file);

          // Esperar a que se complete la subida y obtener la URL
          return new Observable<GenericResponse<ManualList>>((observer) => {
            task.snapshotChanges().subscribe(
              () => {
                // Mostrar progreso si es necesario
              },
              (error) => {
                observer.error({
                  rpta: 0,
                  msg: 'Error al subir el archivo a Firebase',
                  data: null,
                  errors: error,
                });
              },
              () => {
                // Subida completada, obtener URL
                fileRef.getDownloadURL().subscribe(
                  (downloadURL) => {
                    // Reemplazar el archivo con la URL en el FormData
                    data.delete('file');

                    // Verificar si ya existe el campo archivo y eliminarlo
                    if (data.has('archivo')) {
                      data.delete('archivo');
                    }

                    // Agregar la nueva URL
                    data.append('archivo', downloadURL);

                    // Ahora enviar al backend
                    this.http
                      .post<GenericResponse<ManualList>>(this.baseUrl, data)
                      .subscribe(
                        (response) => {
                          observer.next(response);
                        },
                        (error) => {
                          observer.error(error);
                        },
                        () => observer.complete()
                      );
                  },
                  (error) => {
                    observer.error({
                      rpta: 0,
                      msg: 'Error al obtener la URL del archivo',
                      data: null,
                      errors: error,
                    });
                  }
                );
              }
            );
          });
        }
      }
    }

    // Si no hay archivo o no es FormData, enviar directamente
    return this.http.post<GenericResponse<ManualList>>(this.baseUrl, data);
  }

  /**
   * Actualiza un manual existente
   * @param data Datos del manual o FormData
   * @param id ID del manual
   * @returns Observable con la respuesta
   */
  update(
    data: Manual | FormData,
    id: number
  ): Observable<GenericResponse<ManualList>> {
    console.log('Actualizando manual con ID:', id);

    // Validar que el ID sea un número válido
    if (!id || isNaN(Number(id))) {
      console.error('ID inválido para actualizar manual:', id);
      return new Observable<GenericResponse<ManualList>>((observer) => {
        observer.error({
          rpta: 0,
          msg: 'ID inválido para actualizar manual',
          data: null,
          errors: { message: 'El ID proporcionado no es válido' },
        });
      });
    }

    // Convertir a número si es string
    const numericId = Number(id);
    console.log('ID convertido a número:', numericId);

    // Verificar si es FormData y tiene los datos necesarios
    if (data instanceof FormData) {
      // Asegurarse de que el ID no esté en el FormData
      if (data.has('id')) {
        data.delete('id');
      }

      // Verificar si tiene userAuthId (campo esperado por el backend)
      if (!data.has('userAuthId')) {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          try {
            const user = JSON.parse(userStr);
            if (user && user.id) {
              data.append('userAuthId', user.id.toString());

              // Mantener los campos anteriores por compatibilidad
              if (!data.has('userUpdateId')) {
                data.append('userUpdateId', user.id.toString());
              }
              if (!data.has('user_update_id')) {
                data.append('user_update_id', user.id.toString());
              }
            }
          } catch (error) {
            // Error al parsear el usuario del localStorage
          }
        }
      }

      // Si es FormData y contiene un archivo, primero subimos a Firebase
      if (data.has('file')) {
        const file = data.get('file') as File;
        if (file) {
          // Crear un nombre único para el archivo
          const filePath = `manuales/${new Date().getTime()}_${file.name}`;
          const fileRef = this.storage.ref(filePath);
          const task = this.storage.upload(filePath, file);

          // Esperar a que se complete la subida y obtener la URL
          return new Observable<GenericResponse<ManualList>>((observer) => {
            task.snapshotChanges().subscribe(
              () => {
                // Mostrar progreso si es necesario
              },
              (error) => {
                observer.error({
                  rpta: 0,
                  msg: 'Error al subir el archivo a Firebase',
                  data: null,
                  errors: error,
                });
              },
              () => {
                // Subida completada, obtener URL
                fileRef.getDownloadURL().subscribe(
                  (downloadURL) => {
                    // Reemplazar el archivo con la URL en el FormData
                    data.delete('file');

                    // Verificar si ya existe el campo archivo y eliminarlo
                    if (data.has('archivo')) {
                      data.delete('archivo');
                    }

                    // Agregar la nueva URL
                    data.append('archivo', downloadURL);

                    // Ahora enviar al backend
                    this.http
                      .put<GenericResponse<ManualList>>(
                        `${this.baseUrl}/${numericId}`,
                        data
                      )
                      .subscribe(
                        (response) => {
                          observer.next(response);
                        },
                        (error) => {
                          observer.error(error);
                        },
                        () => observer.complete()
                      );
                  },
                  (error) => {
                    observer.error({
                      rpta: 0,
                      msg: 'Error al obtener la URL del archivo',
                      data: null,
                      errors: error,
                    });
                  }
                );
              }
            );
          });
        }
      } else {
        // Si no hay archivo nuevo, enviar la actualización sin archivo
        // Si no hay campo archivo en FormData, significa que no queremos cambiar el archivo existente

        // Enviar la actualización al backend
        return new Observable<GenericResponse<ManualList>>((observer) => {
          this.http
            .put<GenericResponse<ManualList>>(
              `${this.baseUrl}/${numericId}`,
              data
            )
            .subscribe(
              (response) => {
                observer.next(response);
              },
              (error) => {
                console.error('Error en la solicitud PUT:', error);
                observer.error(error);
              },
              () => observer.complete()
            );
        });
      }
    } else if (data instanceof Manual) {
      // Si es un objeto Manual, asegurarse de que no enviamos el ID en el cuerpo
      const manualData: any = { ...data };
      if ('id' in manualData) {
        delete manualData.id;
      }

      // Si el campo archivo está vacío, eliminarlo para mantener el archivo existente
      if (manualData.archivo === '') {
        delete manualData.archivo;
      }

      return this.http.put<GenericResponse<ManualList>>(
        `${this.baseUrl}/${numericId}`,
        manualData
      );
    }

    // Si no hay archivo o no es FormData, enviar directamente
    return this.http.put<GenericResponse<ManualList>>(
      `${this.baseUrl}/${numericId}`,
      data
    );
  }

  /**
   * Elimina un manual
   * @param id ID del manual
   * @returns Observable con la respuesta
   */
  delete(id: number): Observable<GenericResponse<ManualList>> {
    return this.http.delete<GenericResponse<ManualList>>(
      `${this.baseUrl}/${id}`
    );
  }

  /**
   * Restaura un manual eliminado
   * @param id ID del manual
   * @returns Observable con la respuesta
   */
  restore(id: number): Observable<GenericResponse<ManualList>> {
    return this.http.put<GenericResponse<ManualList>>(
      `${this.baseUrl}/restore/${id}`,
      {}
    );
  }

  /**
   * Obtiene manuales paginados
   * @param pagination Parámetros de paginación
   * @returns Observable con la respuesta
   */
  getPagination(pagination: Pagination): Observable<GenericResponse<any>> {
    const { page, perPage, search, column, order } = pagination;
    // Ajustar la página para que coincida con lo que espera el backend
    // Si el backend espera páginas basadas en 0, restar 1
    let url = `${this.baseUrl}?page=${page - 1}&size=${perPage}`;

    if (search) {
      url += `&search=${encodeURIComponent(search)}`;
    }

    if (column) {
      url += `&column=${encodeURIComponent(column)}`;
    }

    if (order) {
      url += `&order=${encodeURIComponent(order)}`;
    }

    console.log('URL de paginación:', url);

    // Usar directamente el método http.get sin crear un nuevo Observable
    // Esto evita posibles duplicaciones en la suscripción
    return this.http.get<GenericResponse<any>>(url).pipe(
      map((response) => {
        console.log('Respuesta del servicio:', response);

        // Verificar si la respuesta es válida
        if (!response) {
          console.error('Respuesta vacía del servidor');
          throw {
            rpta: 0,
            msg: 'Respuesta vacía del servidor',
            data: null,
          };
        }

        // Verificar si la respuesta tiene el formato esperado
        if (response.rpta === undefined) {
          console.warn(
            'La respuesta no tiene el formato esperado, intentando adaptar...'
          );
          // Intentar adaptar la respuesta a un formato compatible
          return {
            rpta: 1,
            msg: 'Datos obtenidos',
            data: response,
          };
        }

        // La respuesta ya tiene el formato esperado
        return response;
      }),
      catchError((error) => {
        console.error('Error en el servicio:', error);
        return throwError(() => ({
          rpta: 0,
          msg: error.message || 'Error al obtener los manuales',
          data: null,
          errors: error,
        }));
      })
    );
  }

  // Métodos del patrón observer
  changeArrayObserver(list: ManualList[]) {
    this.listSubject.next(list);
  }

  addObjectObserver(data: ManualList) {
    const currentList = this.listSubject.getValue();
    this.listSubject.next([...currentList, data]);
  }

  updateObjectObserver(data: ManualList) {
    const currentList = this.listSubject.getValue();
    const index = currentList.findIndex((item) => item.id === data.id);
    if (index !== -1) {
      currentList[index] = data;
      this.listSubject.next([...currentList]);
    }
  }

  removeObjectObserver(id: number) {
    const currentList = this.listSubject.getValue();
    this.listSubject.next(currentList.filter((item) => item.id !== id));
  }
}
